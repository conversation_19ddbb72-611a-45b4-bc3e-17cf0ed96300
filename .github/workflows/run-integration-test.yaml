name: run-integration-test
on:
  workflow_dispatch:
    inputs:
      repo:
        required: true
        description: 'The repository run'
        type: string
      revision:
        required: true
        description: 'The revision run'
        type: string
      script:
        required: true
        description: 'The script to run'
        type: string

jobs:
  run-integration-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4 # https://github.com/marketplace/actions/checkout
        with:
          repository: ${{ inputs.repo }}
          ref: ${{ inputs.revision }}
          token: ${{ secrets.TOKEN }}
      - uses: azure/setup-kubectl@v4 # setup kubectl 
      - uses: jerop/tkn@v0.1.0 # setup tkn cli
      - name: Create k8s Kind Cluster
        uses: helm/kind-action@v1
        with:
          registry: true
          registry_name: my-registry
          registry_port: 5001
          registry_enable_delete: true
      - uses: actions/setup-go@v5
        with:
          go-version: '>=1.23.6'
      - uses: chris<PERSON><PERSON>on/setup-yq@latest
      - uses: ko-build/setup-ko@v0.8
      - uses: philiplehmann/setup-kustomize@v2.2.5
      - name: Run script
        run: |
          set -ex

          echo "==> 🚀 Will start to run script!!"

          ${{ inputs.script }}

          echo "==> 🎉 done!"
