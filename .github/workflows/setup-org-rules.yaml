name: setup-org-rules
on:
  schedule:
    # all input default values should be added into the workflow directly
    # https://stackoverflow.com/questions/72539900/schedule-trigger-github-action-workflow-with-input-parameters
    - cron: '25 6 * * *'

  workflow_dispatch:
    inputs:
      repo:
        required: true
        description: 'The repository run'
        type: string
        default: alaudadevops/hack
      revision:
        required: true
        description: 'The revision run'
        type: string
        default: main
      script:
        required: true
        description: 'The script to run'
        type: string
        default: |
          gh auth login --with-token < secret.token &&
          ./scripts/setup-repos.sh alaudadevops
jobs:
  setup-org-rules:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4 # https://github.com/marketplace/actions/checkout
        with:
          repository: ${{ inputs.repo || 'alaudadevops/hack' }}
          ref: ${{ inputs.revision || 'main' }}
          token: ${{ secrets.TOKEN }}
      - name: Install GH CLI
        uses: dev-hanz-ops/install-gh-cli-action@v0.2.1
      - name: Run script
        run: |
          echo -n "${{ secrets.TOKEN }}" > secret.token

          set -ex

          echo "==> 🚀 Will start to run script!!"

          ${{ inputs.script || 'gh auth login --with-token < secret.token && ./scripts/setup-repos.sh alaudadevops' }}

          echo "==> 🎉 done!"